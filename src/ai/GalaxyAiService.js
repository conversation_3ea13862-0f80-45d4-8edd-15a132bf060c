require('dotenv').config();

export class GalaxyAiService {
	/**
	 *
	 * HOST Server: http://copilot.prodgpu.chinastock.com.cn/
	 *
	 * 系统授权：
	 *
	 * - URL： POST api/system_token
	 * - 请求方式：application/json
	 * - 接口描述：通过 api/system_token 获取的access_token 有效时间为 7 天。（System-Id  和 System-secret 需要联系系统管理员获取）
	 * - 请求参数说明:
	 *
	 * 请求示例
	 * ```json
	 * {
	 *   "user-nonce":"*************",
	 *   "System-Token":"3226fb8fddf3e9db3aaf3f913a5ba0ed",
	 *   "System-Id":"oa-system"
	 *   "account":"***********"
	 * }
	 * ```
	 * - 签名说明:
	 * System-Token 加密方式 ：System-Id  + \t + System-secret   + \t + user-nonce 组成字符串后用MD5加密
	 * - 示例:
	 * system_id = 'oa-system'
	 * system_secret = 'Aa222@b2#Cc3$Dd4%F6^Gh7&8*K90Mn?'
	 * user-nonce为： '*************'
	 * 签名后的 System-Token： '3226fb8fddf3e9db3aaf3f913a5ba0ed'
	 *
	 * ```python
	 * import hashlib
	 * import time
	 *
	 * def md5_encryption(data):
	 *     md5 = hashlib.md5()
	 *     md5.update(data.encode("utf-8"))
	 *     return md5.hexdigest()
	 *
	 *
	 * system_id = 'system_oa'
	 * system_secret = 'Aa222@b2#Cc3$Dd4%F6^Gh7&8*K90Mn?'
	 * user_nonce = str(int(time.time() * 1000))
	 * print("时间戳为：", user_nonce)
	 * print("System-Token：", md5_encryption(f"{system_id}\t{system_secret}\t{user_nonce}"))
	 * ```
	 */
	static async getSystemToken () {
		// system id and system_secret load from environment variables or configuration
		// GALAXY_SYSTEM_ID, GALAXY_SYSTEM_SECRET
	}

	/**
	 * http://copilot.prodgpu.chinastock.com.cn/api/app/xchat
	 *
	 * 调用助手问答接口
	 * - POST api/app/xchat
	 * - 数据传输格式 application/json
	 * 示例：
	 *
	 * ```json
	 * JSON
	 *  {
	 *   "messages": [
	 *     {
	 *       "role": "user",
	 *       "content": "你好呀"
	 *     }
	 *   ],
	 *   "conversation_id": "chat-fzBsPshg2bizKg2dz7kFkE",
	 *   "stream": true,
	 *   "app_id": "app-LQMX9oFMy4TJ36eGUj2sBK",
	 *   "tag": [
	 *     "user-1008611"
	 *   ]
	 * }
	 * ```
	 *
	 * request 参数说明: 需要拿到第一步的模型平台 的 access_token
	 * response 区别于 OpenAI 的 response，是：`$.delta`
	 */
	static async chat (messages, conversationId, appId, tags = []) {
		///
	}
}
