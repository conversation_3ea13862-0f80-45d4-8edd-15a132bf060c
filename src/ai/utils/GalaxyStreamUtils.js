import { createCallbacksTransformer, createStreamDataTransformer } from "ai";

const utf8Decoder = new TextDecoder("utf-8");

/**
 * 创建 Galaxy 平台的流式响应解析器
 * 处理 Galaxy 平台特有的 $.delta 格式
 * @param {Response} res - fetch 响应对象
 * @returns {AsyncGenerator} 异步生成器
 */
export async function* createGalaxyParser(res) {
	const reader = res.body?.getReader();
	if (!reader) throw new Error("No reader available in response");

	let buffer = "";
	while (true) {
		const { value: chunk, done } = await reader.read();
		if (done) break;

		buffer += utf8Decoder.decode(chunk, { stream: true });
		const lines = buffer.split("\n");

		buffer = lines.pop() || "";

		for (const line of lines) {
			// 跳过空行和 ping 事件
			if (line.trim() === "" || line === "event: ping") {
				continue;
			}

			// 处理 data: 前缀
			const message = line.replace(/^data: /, "");
			
			// 检查结束标记
			if (message === "[DONE]") {
				break;
			}

			try {
				const parsed = JSON.parse(message);
				// Galaxy 平台使用 $.delta 格式，而不是 OpenAI 的 $.choices[0].delta.content
				if (parsed.delta) {
					yield parsed.delta;
				}
			} catch (error) {
				// 忽略 JSON 解析错误，继续处理下一行
				console.warn("Could not JSON parse Galaxy stream message", message, error);
			}
		}
	}
}

/**
 * 创建 Galaxy 平台的流式响应解析器（用于 axios 响应）
 * @param {Object} response - axios 响应对象
 * @returns {AsyncGenerator} 异步生成器
 */
export async function* createGalaxyAxiosParser(response) {
	let buffer = "";
	
	return new Promise((resolve, reject) => {
		const generator = (async function* () {
			response.data.on('data', (chunk) => {
				buffer += utf8Decoder.decode(chunk, { stream: true });
				const lines = buffer.split('\n');
				buffer = lines.pop() || '';

				for (const line of lines) {
					if (line.trim() === '' || line === 'event: ping') {
						continue;
					}

					const message = line.replace(/^data: /, '');
					if (message === '[DONE]') {
						return;
					}

					try {
						const parsed = JSON.parse(message);
						if (parsed.delta) {
							generator.next(parsed.delta);
						}
					} catch (error) {
						console.warn("Could not JSON parse Galaxy stream message", message, error);
					}
				}
			});

			response.data.on('end', () => {
				resolve();
			});

			response.data.on('error', (error) => {
				reject(error);
			});
		})();

		resolve(generator);
	});
}

/**
 * 将 Galaxy 流转换为可读流
 * @param {AsyncGenerator} galaxyStream - Galaxy 流生成器
 * @returns {ReadableStream} 可读流
 */
export function galaxyStreamToReadableStream(galaxyStream) {
	const it = galaxyStream[Symbol.asyncIterator]();
	const trimStartOfStream = trimStartOfStreamHelper();

	return new ReadableStream({
		async pull(controller) {
			const { value, done } = await it.next();
			if (done) {
				controller.close();
				return;
			}
			
			// 处理 Galaxy 平台的 delta 格式
			let text = '';
			if (typeof value === 'string') {
				text = value;
			} else if (value && value.content) {
				text = value.content;
			} else if (value && value.text) {
				text = value.text;
			}

			const trimmedText = trimStartOfStream(text);
			if (trimmedText) {
				controller.enqueue(trimmedText);
			}
		},
	});
}

/**
 * 辅助函数：去除流开始的空白字符
 * @returns {Function} 处理函数
 */
export function trimStartOfStreamHelper() {
	let isStreamStart = true;

	return (text) => {
		if (isStreamStart) {
			text = text.trimStart();
			if (text) isStreamStart = false;
		}
		return text;
	};
}

/**
 * 将 Galaxy 响应转换为数据流
 * @param {Response} response - fetch 响应对象
 * @returns {ReadableStream} 数据流
 */
export function galaxyResponseToDataStream(response) {
	const dataStream = createGalaxyParser(response);
	return galaxyStreamToReadableStream(dataStream)
		.pipeThrough(createCallbacksTransformer(undefined))
		.pipeThrough(createStreamDataTransformer());
}

/**
 * 将 Galaxy axios 响应转换为流式完成
 * @param {Object} response - axios 响应对象
 * @returns {ReadableStream} 流式完成
 */
export function convertGalaxyToStreamCompletion(response) {
	let controller;
	const stream = new ReadableStream({
		async start(con) {
			controller = con;
		},
	});

	let buffer = "";
	response.data.on("data", async (data) => {
		if (data) {
			const dataStr = data.toString();
			buffer += dataStr;

			const lines = buffer.split("\n");
			buffer = lines.pop() || "";

			for (const line of lines) {
				if (line.trim() === "" || line === "event: ping") {
					continue;
				}

				const message = line.replace(/^data: /, "");
				if (message === "[DONE]") {
					controller.close();
				} else {
					try {
						const parsed = JSON.parse(message);
						// Galaxy 平台使用 $.delta 格式
						if (parsed.delta) {
							controller.enqueue(parsed.delta);
						}
					} catch (error) {
						console.warn("Could not JSON parse Galaxy stream message", message, error);
					}
				}
			}
		}
	});

	response.data.on('end', () => {
		controller.close();
	});

	response.data.on('error', (error) => {
		controller.error(error);
	});

	return stream;
}

/**
 * 创建 Galaxy 兼容的文本流
 * @param {ReadableStream} galaxyStream - Galaxy 流
 * @returns {ReadableStream} 文本流
 */
export function createGalaxyTextStream(galaxyStream) {
	return new ReadableStream({
		start(controller) {
			const reader = galaxyStream.getReader();
			
			function pump() {
				return reader.read().then(({ done, value }) => {
					if (done) {
						controller.close();
						return;
					}
					
					// 处理 Galaxy 的 delta 格式
					let text = '';
					if (typeof value === 'string') {
						text = value;
					} else if (value && value.content) {
						text = value.content;
					} else if (value && value.text) {
						text = value.text;
					}
					
					if (text) {
						controller.enqueue(text);
					}
					
					return pump();
				}).catch(error => {
					controller.error(error);
				});
			}
			
			return pump();
		}
	});
}

/**
 * 将 Galaxy 流转换为 AI.js 兼容格式
 * @param {ReadableStream} galaxyStream - Galaxy 流
 * @returns {Object} AI.js 兼容的流对象
 */
export function galaxyStreamToAIJS(galaxyStream) {
	const textStream = createGalaxyTextStream(galaxyStream);
	
	return {
		textStream,
		// 可以添加其他 AI.js 兼容的属性
		usage: {},
		finishReason: 'stop'
	};
}
