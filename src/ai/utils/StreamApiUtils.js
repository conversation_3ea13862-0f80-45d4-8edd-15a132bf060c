import { createCallbacksTransformer, createStreamDataTransformer } from "ai";
const utf8Decoder = new TextDecoder("utf-8");

export async function* createDifyParser(res) {
	const reader = res.body?.getReader();
	if (!reader) throw new Error("No reader");

	let buffer = "";
	while (true) {
		const { value: chunk, done } = await reader.read();
		if (done) break;

		buffer += utf8Decoder.decode(chunk, { stream: true });
		const lines = buffer.split("\n");

		buffer = lines.pop() || "";

		for (const line of lines) {
			if (line === "event: ping") {
				continue;
			}

			const message = line.replace(/^data: /, "");
			if (message === "[DONE]") {
				break;
			} else {
				try {
					const parsed = JSON.parse(message);
					if (parsed.delta) {
						yield parsed.delta;
					}
				} catch (error) {
					// console.warn("Could not JSON parse stream message", message, error);
				}
			}
		}
	}
}

export function trimStartOfStreamHelper() {
	let isStreamStart = true;

	return (text) => {
		if (isStreamStart) {
			text = text.trimStart();
			if (text) isStreamStart = false;
		}
		return text;
	};
}

export function toReadableStream(res) {
	const it = res[Symbol.asyncIterator]();
	const trimStartOfStream = trimStartOfStreamHelper();

	return new ReadableStream({
		async pull(controller) {
			const { value, done } = await it.next();
			if (done) {
				controller.close();
				return;
			}
			const text = trimStartOfStream(value ?? "");
			if (text) {
				controller.enqueue(text);
			}
		},
	});
}

export function difyResponseToDataStream(response) {
	const dataStream = createDifyParser(response);
	return toReadableStream(dataStream)
		.pipeThrough(createCallbacksTransformer(undefined))
		.pipeThrough(createStreamDataTransformer());
}

export function convertToStreamCompletion(response) {
	let controller;
	const stream = new ReadableStream({
		async start(con) {
			controller = con;
		},
	});

	let buffer = "";
	response.data.on("data", async (data) => {
		if (data) {
			const dataStr = data.toString();
			buffer += dataStr;

			const lines = buffer.split("\n");
			buffer = lines.pop() || "";

			for (const line of lines) {
				const message = line.replace(/^data: /, "");
				if (message === "[DONE]") {
					controller.close();
				} else if (message === "event: ping") {
					controller.enqueue("");
				} else {
					try {
						const parsed = JSON.parse(message);
						if (parsed.delta) {
							controller.enqueue(parsed.delta);
						}
					} catch (error) {
						// console.warn("Could not JSON parse stream message", message, error);
					}
				}
			}
		}
	});

	return stream;
}
