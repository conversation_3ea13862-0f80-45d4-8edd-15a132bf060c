require('dotenv').config();
const axios = require('axios');

/**
 * Galaxy AI 基类
 * 提供自定义 request/response 格式支持，处理 Galaxy 平台特有的认证和响应格式
 */
class GalaxyAiBase {
	constructor(options = {}) {
		this.baseURL = options.baseURL || 'http://copilot.prodgpu.chinastock.com.cn';
		this.timeout = options.timeout || 30000;
		this.retryAttempts = options.retryAttempts || 3;
		this.retryDelay = options.retryDelay || 1000;
		
		// 创建 axios 实例
		this.httpClient = axios.create({
			baseURL: this.baseURL,
			timeout: this.timeout,
			headers: {
				'Content-Type': 'application/json',
				'User-Agent': 'Galaxy-Transit-Tool/1.0.0'
			}
		});

		// 添加请求拦截器
		this.httpClient.interceptors.request.use(
			(config) => this._onRequest(config),
			(error) => this._onRequestError(error)
		);

		// 添加响应拦截器
		this.httpClient.interceptors.response.use(
			(response) => this._onResponse(response),
			(error) => this._onResponseError(error)
		);
	}

	/**
	 * 请求拦截器 - 在发送请求前调用
	 * @param {Object} config - axios 配置
	 * @returns {Object} 修改后的配置
	 */
	_onRequest(config) {
		// 添加时间戳
		config.metadata = { startTime: Date.now() };
		
		// 可以在这里添加通用的请求头或参数
		if (this.accessToken) {
			config.headers.Authorization = `Bearer ${this.accessToken}`;
		}

		return config;
	}

	/**
	 * 请求错误拦截器
	 * @param {Error} error - 请求错误
	 * @returns {Promise} 拒绝的 Promise
	 */
	_onRequestError(error) {
		console.error('Galaxy AI Request Error:', error.message);
		return Promise.reject(error);
	}

	/**
	 * 响应拦截器 - 在接收响应后调用
	 * @param {Object} response - axios 响应
	 * @returns {Object} 修改后的响应
	 */
	_onResponse(response) {
		// 计算请求耗时
		if (response.config.metadata) {
			const duration = Date.now() - response.config.metadata.startTime;
			response.duration = duration;
		}

		// 处理 Galaxy 平台特有的响应格式
		return this._transformResponse(response);
	}

	/**
	 * 响应错误拦截器
	 * @param {Error} error - 响应错误
	 * @returns {Promise} 拒绝的 Promise
	 */
	async _onResponseError(error) {
		const config = error.config;
		
		// 如果没有配置或已经重试过最大次数，直接抛出错误
		if (!config || config._retryCount >= this.retryAttempts) {
			return Promise.reject(this._transformError(error));
		}

		// 增加重试计数
		config._retryCount = config._retryCount || 0;
		config._retryCount++;

		// 等待一段时间后重试
		await this._delay(this.retryDelay * config._retryCount);
		
		return this.httpClient(config);
	}

	/**
	 * 转换响应格式
	 * @param {Object} response - 原始响应
	 * @returns {Object} 转换后的响应
	 */
	_transformResponse(response) {
		// Galaxy 平台可能有特殊的响应格式，在这里进行标准化
		if (response.data) {
			// 检查是否有错误
			if (response.data.error) {
				throw new Error(`Galaxy AI Error: ${response.data.error.message || response.data.error}`);
			}

			// 标准化成功响应
			if (response.data.success !== undefined && !response.data.success) {
				throw new Error(`Galaxy AI Request Failed: ${response.data.message || 'Unknown error'}`);
			}
		}

		return response;
	}

	/**
	 * 转换错误格式
	 * @param {Error} error - 原始错误
	 * @returns {Error} 转换后的错误
	 */
	_transformError(error) {
		if (error.response) {
			// 服务器响应了错误状态码
			const status = error.response.status;
			const data = error.response.data;
			
			let message = `Galaxy AI HTTP Error ${status}`;
			if (data && data.message) {
				message += `: ${data.message}`;
			} else if (data && typeof data === 'string') {
				message += `: ${data}`;
			}
			
			const transformedError = new Error(message);
			transformedError.status = status;
			transformedError.response = error.response;
			return transformedError;
		} else if (error.request) {
			// 请求已发出但没有收到响应
			return new Error('Galaxy AI Network Error: No response received');
		} else {
			// 其他错误
			return new Error(`Galaxy AI Error: ${error.message}`);
		}
	}

	/**
	 * 延迟函数
	 * @param {number} ms - 延迟毫秒数
	 * @returns {Promise} 延迟 Promise
	 */
	_delay(ms) {
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * 发送 GET 请求
	 * @param {string} url - 请求 URL
	 * @param {Object} params - 查询参数
	 * @param {Object} options - 请求选项
	 * @returns {Promise<Object>} 响应数据
	 */
	async get(url, params = {}, options = {}) {
		const response = await this.httpClient.get(url, {
			params,
			...options
		});
		return response.data;
	}

	/**
	 * 发送 POST 请求
	 * @param {string} url - 请求 URL
	 * @param {Object} data - 请求数据
	 * @param {Object} options - 请求选项
	 * @returns {Promise<Object>} 响应数据
	 */
	async post(url, data = {}, options = {}) {
		const response = await this.httpClient.post(url, data, options);
		return response.data;
	}

	/**
	 * 发送流式 POST 请求
	 * @param {string} url - 请求 URL
	 * @param {Object} data - 请求数据
	 * @param {Object} options - 请求选项
	 * @returns {Promise<Object>} 响应对象（包含流）
	 */
	async postStream(url, data = {}, options = {}) {
		const response = await this.httpClient.post(url, data, {
			responseType: 'stream',
			...options
		});
		return response;
	}

	/**
	 * 设置访问令牌
	 * @param {string} token - 访问令牌
	 */
	setAccessToken(token) {
		this.accessToken = token;
	}

	/**
	 * 获取客户端状态
	 * @returns {Object} 客户端状态
	 */
	getClientStatus() {
		return {
			baseURL: this.baseURL,
			timeout: this.timeout,
			retryAttempts: this.retryAttempts,
			hasToken: !!this.accessToken
		};
	}
}

module.exports = GalaxyAiBase;
