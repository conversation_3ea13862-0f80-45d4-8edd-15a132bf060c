const fs = require('fs-extra');
const path = require('path');
const glob = require('glob');
const chalk = require('chalk');

/**
 * Sass 变量注册表
 * 用于扫描和索引项目中的所有 Sass 变量定义
 */
class SassVariableRegistry {
  constructor(projectPath) {
    this.projectPath = path.resolve(projectPath);
    this.variables = new Map(); // 变量名 -> 定义信息数组
    this.fileVariables = new Map(); // 文件路径 -> 该文件定义的变量列表
    this.centralVariableFiles = []; // 集中的变量文件列表
  }

  async buildRegistry() {
    console.log(chalk.blue('🔍 开始扫描 Sass 变量...'));

    try {
      const sassFiles = await this.findSassFiles();

      // 首先识别可能的集中变量文件
      await this.identifyCentralVariableFiles(sassFiles);

      // 然后解析所有文件中的变量
      for (const filePath of sassFiles) {
        await this.parseVariablesInFile(filePath);
      }

      console.log(chalk.green(`✅ 变量扫描完成，共发现 ${this.variables.size} 个变量，${this.centralVariableFiles.length} 个集中变量文件`));

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error(chalk.red(`变量扫描失败: ${error.message}`));
      return false;
    }
  }

  /**
   * 查找项目中的所有 Sass 文件
   */
  async findSassFiles() {
    try {
      // 检查项目路径是否存在
      if (!await fs.pathExists(this.projectPath)) {
        return [];
      }

      const files = glob.sync('**/*.{scss,sass}', {
        cwd: this.projectPath,
        absolute: true,
        ignore: ['node_modules/**', 'dist/**', 'build/**']
      });

      return files;
    } catch (error) {
      console.warn(chalk.yellow(`查找Sass文件失败: ${error.message}`));
      return [];
    }
  }

  /**
   * 识别集中的变量文件
   */
  async identifyCentralVariableFiles(sassFiles) {
    // 常见的变量文件模式
    const variableFilePatterns = [
      /variables\.scss$/,
      /vars\.scss$/,
      /variables\/index\.scss$/,
      /styles\/variables\.scss$/,
      /styles\/vars\.scss$/,
      /theme\/variables\.scss$/
    ];

    // 检查文件名匹配模式
    for (const filePath of sassFiles) {
      for (const pattern of variableFilePatterns) {
        if (pattern.test(filePath)) {
          this.centralVariableFiles.push(filePath);
          console.log(chalk.gray(`找到集中变量文件: ${path.relative(this.projectPath, filePath)}`));
          break;
        }
      }
    }

    // 检查文件内容，如果文件包含大量变量定义，也将其视为集中变量文件
    for (const filePath of sassFiles) {
      if (this.centralVariableFiles.includes(filePath)) continue;

      try {
        const content = await fs.readFile(filePath, 'utf8');
        const variableDefCount = (content.match(/\$[a-zA-Z0-9_-]+\s*:/g) || []).length;

        // 如果文件包含超过10个变量定义，将其视为集中变量文件
        if (variableDefCount > 10) {
          this.centralVariableFiles.push(filePath);
          console.log(chalk.gray(`发现变量集中文件: ${path.relative(this.projectPath, filePath)} (包含 ${variableDefCount} 个变量)`));
        }
      } catch (error) {
        // 忽略读取错误
      }
    }
  }

  /**
   * 解析单个文件中的变量定义
   */
  async parseVariablesInFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const variableDefRegex = /\$([a-zA-Z0-9_-]+)\s*:\s*([^;]+);/g;
      let match;

      while ((match = variableDefRegex.exec(content)) !== null) {
        const variableName = match[1];
        const variableValue = match[2].trim();

        // 添加到变量映射
        if (!this.variables.has(variableName)) {
          this.variables.set(variableName, []);
        }

        this.variables.get(variableName).push({
          file: filePath,
          value: variableValue,
          isCentralFile: this.centralVariableFiles.includes(filePath)
        });

        // 添加到文件变量映射
        if (!this.fileVariables.has(filePath)) {
          this.fileVariables.set(filePath, new Set());
        }

        this.fileVariables.get(filePath).add(variableName);
      }
    } catch (error) {
      // 忽略读取错误
    }
  }

  /**
   * 查找变量的定义源，优先返回集中变量文件中的定义
   */
  findVariableSource(variableName) {
    const definitions = this.variables.get(variableName) || [];

    // 优先返回集中变量文件中的定义
    const centralDefs = definitions.filter(def => def.isCentralFile);
    if (centralDefs.length > 0) {
      return centralDefs.map(def => def.file);
    }

    // 否则返回所有定义
    return definitions.map(def => def.file);
  }

  hasVariable(variableName) {
    return this.variables.has(variableName);
  }

  /**
   * 检查是否为集中变量文件
   */
  isCentralVariableFile(filePath) {
    return this.centralVariableFiles.includes(filePath);
  }

  /**
   * 获取推荐的变量导入文件（智能推荐）
   */
  getRecommendedImports(filePath, usedVariables) {
    const recommendations = new Map();

    for (const variableName of usedVariables) {
      const sources = this.findVariableSource(variableName);

      if (sources.length > 0) {
        // 优先推荐集中变量文件
        const centralSources = sources.filter(source => this.isCentralVariableFile(source));
        const targetSource = centralSources.length > 0 ? centralSources[0] : sources[0];

        if (!recommendations.has(targetSource)) {
          recommendations.set(targetSource, new Set());
        }
        recommendations.get(targetSource).add(variableName);
      }
    }

    return recommendations;
  }

  /**
   * 生成导入语句建议
   */
  generateImportSuggestions(filePath, usedVariables) {
    const recommendations = this.getRecommendedImports(filePath, usedVariables);
    const suggestions = [];

    for (const [sourcePath, variables] of recommendations) {
      const relativePath = path.relative(path.dirname(filePath), sourcePath);
      const normalizedPath = relativePath.startsWith('.') ? relativePath : './' + relativePath;

      // 移除.scss扩展名（Sass导入时通常省略）
      const importPath = normalizedPath.replace(/\.scss$/, '');

      suggestions.push({
        importPath,
        sourcePath,
        variables: [...variables],
        isCentralFile: this.isCentralVariableFile(sourcePath),
        statement: `@use '${importPath}' as *;`
      });
    }

    return suggestions;
  }
}

module.exports = SassVariableRegistry;
