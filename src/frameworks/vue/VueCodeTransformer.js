const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const glob = require('glob')
const gogocode = require('gogocode')
const { transform: vueTransform } = require('gogocode-plugin-vue')
const { transform: elementTransform } = require('@unit-mesh/gogocode-plugin-element')
const FailureLogger = require('../../utils/FailureLogger');
const CustomVueTransformer = require('./CustomVueTransformer');
const RenderFunctionTransformer = require('./RenderFunctionTransformer');
const VueRouterTransformer = require('./VueRouterTransformer');
const SafeGogocodeTransform = require('./GogocodeTransform');

/**
 * Vue 代码迁移器
 * 支持从源目录迁移到目标目录，或原地修改
 */
class VueCodeTransformer {
	constructor (inputPath, options = {}) {
		this.inputPath = path.resolve(inputPath)
		this.options = Object.assign({
			srcDir: 'src',
			outputSrcDir: 'src',
			includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
			excludePatterns: ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.*/**'],
			isOutputMode: false,
			outputPath: '',
			verbose: false
		}, options)
		this.isOutputMode = this.options.isOutputMode
		this.outputPath = this.options.outputPath
		this.projectPath = this.inputPath
		this.stats = {
			success: 0,
			failed: 0,
			skipped: 0,
			copied: 0,
			total: 0,
			failedFiles: []
		}
		this.failureLogger = new FailureLogger(this.inputPath);

		this.analysisResult = null

		// 初始化转换器
		this.customVueTransformer = new CustomVueTransformer({
			verbose: this.options.verbose
		});

		this.renderFunctionTransformer = new RenderFunctionTransformer({
			verbose: this.options.verbose
		});

		this.vueRouterTransformer = new VueRouterTransformer({
			verbose: this.options.verbose
		});

		this.gogocodeTransformer = new SafeGogocodeTransform({
			verbose: this.options.verbose,
			maxRetries: 3,
			fallbackToOriginal: true
		});
	}

	/**
	 * 执行迁移
	 */
	async migrate () {
		console.log(chalk.blue('🔄 开始 Vue 代码迁移 (原地修改模式)...'))

		try {
			await this.ensureOutputDirectory()

			const files = await this.getFilesToMigrate()
			console.log(chalk.gray(`找到 ${files.length} 个文件需要迁移`))

			this.stats.total = files.length

			await this.failureLogger.initialize();

			await this.performGogocodeTransformation(files)

			await this.cleanupGogocodeTransferFiles()

			await this.copyGogocodeTransferFile()

			if (this.failureLogger) {
				await this.failureLogger.saveFailures();
			}

			this.printMigrationStats()

			if (this.getTransformErrorStats().total > 0 || this.stats.failed > 0) {
				await this.saveErrorReport()
			}

			return this.stats

		} catch (error) {
			console.error(chalk.red('❌ 迁移失败:'), error.message)
			throw error
		}
	}

	/**
	 * 使用 Gogocode 批量转换文件
	 */
	async performGogocodeTransformation(files) {
		console.log(chalk.gray(`开始使用 Gogocode 转换 ${files.length} 个文件...`));
		for (const filePath of files) {
			await this.migrateFile(filePath)
		}

		console.log(chalk.green(`✅ Gogocode 转换完成: 成功 ${this.stats.success}, 失败 ${this.stats.failed}`));

		if (this.stats.failed > 0) {
			console.log(chalk.yellow(`⚠️  发现 ${this.stats.failed} 个转换失败的文件，将在后续步骤中使用 AI 修复`));
		}
	}

	async ensureOutputDirectory () {
		if (this.isOutputMode) {
			const outputSrcPath = path.join(this.outputPath, this.options.outputSrcDir)
			await fs.ensureDir(outputSrcPath)
			console.log(chalk.gray(`已创建输出目录: ${outputSrcPath}`))
		}
	}

	/**
	 * 复制 gogocodeTransfer.js 工具文件到目标项目
	 *
	 * - '@/utils/gogocodeTransfer'
	 */
	async copyGogocodeTransferFile () {
		try {
			// gogocodeTransfer.js 源文件路径（在迁移工具中）
			const sourceTransferFile = path.join(__dirname, 'utils', 'gogocodeTransfer.js')

			// 目标路径
			const targetPath = this.isOutputMode ? this.outputPath : this.inputPath
			const targetUtilsDir = path.join(targetPath, this.options.outputSrcDir || this.options.srcDir, 'utils')
			const targetTransferFile = path.join(targetUtilsDir, 'gogocodeTransfer.js')

			// 检查源文件是否存在
			if (await fs.pathExists(sourceTransferFile)) {
				// 确保目标 utils 目录存在
				await fs.ensureDir(targetUtilsDir)

				// 复制文件
				await fs.copy(sourceTransferFile, targetTransferFile)
				console.log(chalk.green(`✅ 已复制 gogocodeTransfer.js 到: ${path.relative(targetPath, targetTransferFile)}`))
			} else {
				console.log(chalk.yellow(`⚠️  未找到 gogocodeTransfer.js 源文件: ${sourceTransferFile}`))
			}
		} catch (error) {
			console.log(chalk.yellow(`⚠️  复制 gogocodeTransfer.js 失败: ${error.message}`))
		}
	}

	/**
	 * 获取需要迁移的文件列表
	 */
	async getFilesToMigrate () {
		const srcPath = path.join(this.inputPath, this.options.srcDir)
		const files = []

		// 检查源目录是否存在
		if (!await fs.pathExists(srcPath)) {
			throw new Error(`源目录不存在: ${srcPath}`)
		}

		for (const pattern of this.options.includePatterns) {
			const matchedFiles = glob.sync(pattern, {
				cwd: srcPath,
				ignore: this.options.excludePatterns,
				absolute: false
			})

			files.push(...matchedFiles.map(file => path.join(srcPath, file)))
		}

		// 去重
		return [...new Set(files)]
	}

	async migrateFile (filePath) {
		try {
			const relativePath = path.relative(this.inputPath, filePath)
			process.stdout.write(chalk.gray(`迁移: ${relativePath} ... `))

			const source = await fs.readFile(filePath, 'utf8')

			let transformedCode
			const ext = path.extname(filePath)

			if (ext === '.vue' || ext === '.js' ) {
				transformedCode = await this.migrateVueFile(source, filePath)
			} else {
				this.stats.skipped++
				await this.failureLogger.logSkipped(filePath, `不支持的文件类型: ${ext}`)
				return
			}

			const outputFilePath = this.getOutputFilePath(filePath)
			if (transformedCode && (transformedCode !== source || this.isOutputMode)) {
				await fs.ensureDir(path.dirname(outputFilePath))
				await fs.writeFile(outputFilePath, transformedCode, 'utf8')

				if (this.isOutputMode && outputFilePath !== filePath) {
					console.log(chalk.green('✅ (复制+转换)'))
					this.stats.copied++
					await this.failureLogger.logSuccess(filePath, {
						type: 'copy_and_transform',
						outputPath: outputFilePath,
						hasChanges: true
					})
				} else {
					console.log(chalk.green('✅ (转换) file://' + filePath))
				}
				this.stats.success++
				await this.failureLogger.logSuccess(filePath, {
					type: 'transform',
					hasChanges: true,
					originalSize: source.length,
					transformedSize: transformedCode.length
				})
			} else {
				if (this.isOutputMode) {
					// 输出模式下，即使没有变化也要复制文件
					await fs.ensureDir(path.dirname(outputFilePath))
					await fs.copy(filePath, outputFilePath)
					console.log(chalk.gray('复制（无变化）'))
					this.stats.copied++
					await this.failureLogger.logSuccess(filePath, {
						type: 'copy_only',
						outputPath: outputFilePath,
						hasChanges: false
					})
				} else {
					console.log(chalk.gray('跳过（无变化）'))
				}
				this.stats.skipped++
				await this.failureLogger.logSkipped(filePath, '文件内容无变化')
			}

		} catch (error) {
			console.log(chalk.red('❌'))
			this.stats.failed++

			// 确保错误信息是字符串
			const errorMessage = typeof error === 'string' ? error :
								(error && error.message) ? error.message :
								(error && typeof error === 'object') ? JSON.stringify(error) :
								String(error);

			// 记录详细的失败信息，包括绝对路径
			const failureInfo = {
				file: path.relative(this.inputPath, filePath),
				absolutePath: filePath,
				error: errorMessage,
				errorType: this.categorizeError(errorMessage),
				timestamp: new Date().toISOString()
			}

			this.stats.failedFiles.push(failureInfo)

			// 记录失败到 FailureLogger
			await this.failureLogger.logFailure(filePath, errorMessage, {
				fileType: path.extname(filePath),
				errorType: this.categorizeError(errorMessage)
			})

			// 如果是 Gogocode 相关错误，记录更多信息
			if (errorMessage.includes('gogocode') || errorMessage.includes('eventsApi')) {
				console.log(chalk.yellow(`    Gogocode 转换错误: ${failureInfo.file}`))
			}

			// 添加调试信息
			if (this.options.verbose) {
				console.log(chalk.gray(`    错误类型: ${failureInfo.errorType}`))
				console.log(chalk.gray(`    错误详情: ${errorMessage.substring(0, 100)}...`))
			}
		}
	}

	/**
	 * 获取输出文件路径
	 */
	getOutputFilePath (inputFilePath) {
		if (!this.isOutputMode) {
			return inputFilePath
		}

		// 计算相对于输入源目录的路径
		const inputSrcPath = path.join(this.inputPath, this.options.srcDir)
		const relativeToSrc = path.relative(inputSrcPath, inputFilePath)

		// 构建输出路径
		const outputSrcPath = path.join(this.outputPath, this.options.outputSrcDir)
		return path.join(outputSrcPath, relativeToSrc)
	}

	vue2To3Transform(source, filePath) {
		return this.gogocodeTransformer.safeVueTransform(
			vueTransform,
			{
				path: filePath,
				source: source,
			},
			{
				gogocode: gogocode,
			},
			{
				rootPath: path.resolve(this.projectPath, this.options.srcDir),
				outFilePath: filePath,
				outRootPath: path.dirname(filePath),
			}
		)
	}

	elementUiTransform(source, filePath) {
		return this.gogocodeTransformer.safeVueTransform(
			elementTransform,
			{
				path: filePath,
				source: source,
			},
			{
				gogocode: gogocode,
			},
			{
				rootPath: path.resolve(this.projectPath, this.options.srcDir),
				outFilePath: filePath,
				outRootPath: path.dirname(filePath),
			}
		)
	}

	async migrateVueFile (source, filePath) {
		try {
			let transformedCode = source
			const transformSteps = []

			// 1. Vue 2.x 转换
			try {
				const vueTransformed = await this.vue2To3Transform(source, filePath)
				if (vueTransformed !== source) {
					transformedCode = vueTransformed
					transformSteps.push('Vue转换')
				}
			} catch (vueError) {
				console.warn(chalk.yellow(`Vue 转换异常: ${vueError.message}`))
				this.recordTransformError(filePath, 'vue-transform', vueError.message)
			}

			// 2. 增强的 Element 转换
			try {
				const elementTransformed = await this.elementUiTransform(transformedCode, filePath)
				if (elementTransformed !== transformedCode) {
					transformedCode = elementTransformed
					transformSteps.push('Element转换')
				}
			} catch (elementError) {
				// 用于支持在 WebStorm, IDEA 中跳转
				console.warn(chalk.yellow(`file://${filePath}`))
				console.warn(chalk.yellow(`Element 转换异常: ${elementError.message}`))
				this.recordTransformError(filePath, 'element-transform', elementError.message)
			}

			// 3. 应用自定义转换规则
			try {
				const customTransformed = await this.customVueTransformer.transform(transformedCode, filePath)
				if (customTransformed !== transformedCode) {
					transformedCode = customTransformed
					transformSteps.push('自定义转换')
				}
			} catch (customError) {
				console.warn(chalk.yellow(`自定义转换异常: ${customError.message}`))
				this.recordTransformError(filePath, 'custom-transform', customError.message)
			}

			// 4. 应用 Vue Router 转换
			try {
				if (this.vueRouterTransformer.hasVueRouterContent(transformedCode)) {
					const vueRouterTransformed = await this.vueRouterTransformer.transform(transformedCode, filePath);
					if (vueRouterTransformed !== transformedCode) {
						transformedCode = vueRouterTransformed;
						transformSteps.push('VueRouter转换');
					}
				}
			} catch (vueRouterError) {
				console.warn(chalk.yellow(`Vue Router转换异常: ${vueRouterError.message}`))
				this.recordTransformError(filePath, 'vue-router-transform', vueRouterError.message)
			}

			// 5. 应用 Render 转换（使用 AST）
			try {
				if (this.renderFunctionTransformer.hasRenderContent(transformedCode)) {
					const renderContentTransformed = await this.renderFunctionTransformer.transform(transformedCode, filePath);
					if (renderContentTransformed !== transformedCode) {
						transformedCode = renderContentTransformed;
						transformSteps.push('RenderContent转换');
					}
				}
			} catch (renderContentError) {
				console.warn(chalk.yellow(`RenderContent转换异常: ${renderContentError.message}`))
				this.recordTransformError(filePath, 'renderContent-transform', renderContentError.message)
			}

			const hasChanges = transformedCode !== source

			if (this.options.verbose && transformSteps.length > 0) {
				console.log(chalk.gray(`  转换步骤: ${transformSteps.join(' → ')}`))
			}

			if (hasChanges) {
				this.stats.success++
			} else {
				this.stats.skipped++
			}

			return transformedCode

		} catch (error) {
			console.error(chalk.red(`❌ 迁移文件失败: ${filePath}`), error.message)
			this.stats.failed++
			this.stats.failedFiles.push({
				file: path.relative(this.projectPath, filePath),
				absolutePath: filePath,
				error: error.message
			})
			this.recordTransformError(filePath, 'migration-failed', error.message)
			return source
		}
	}

	recordTransformError(filePath, errorType, errorMessage) {
		if (!this.transformErrors) {
			this.transformErrors = []
		}

		this.stats.failed++
		this.transformErrors.push({
			file: path.relative(this.projectPath, filePath),
			absolutePath: filePath,
			errorType: errorType,
			errorMessage: errorMessage,
			timestamp: new Date().toISOString()
		})
	}

	getTransformErrorStats() {
		// 合并自定义错误和错误处理器的错误
		const customErrors = this.transformErrors || []
		const handlerErrors = this.gogocodeTransformer.getErrorStats()

		const allErrors = [...customErrors, ...handlerErrors.errors]
		const byType = {}

		allErrors.forEach(error => {
			const errorType = error.errorType || 'unknown'
			byType[errorType] = (byType[errorType] || 0) + 1
		})

		return {
			total: allErrors.length,
			byType: byType,
			errors: allErrors
		}
	}

	printMigrationStats () {
		console.log('\n' + chalk.bold('📊 代码迁移统计:'))
		console.log(`总计: ${this.stats.total} 个文件`)
		console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`))
		console.log(chalk.gray(`⏸️  跳过: ${this.stats.skipped} 个`))
		console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`))

		if (this.isOutputMode) {
			console.log(chalk.blue(`📁 复制: ${this.stats.copied} 个文件`))
			console.log(chalk.gray(`输出路径: ${this.outputPath}`))
		}

		// 显示转换错误统计
		const errorStats = this.getTransformErrorStats()
		if (errorStats.total > 0) {
			console.log(chalk.yellow(`\n⚠️  转换警告: ${errorStats.total} 个`))
			Object.entries(errorStats.byType).forEach(([type, count]) => {
				console.log(chalk.gray(`  ${type}: ${count} 个`))
			})
		}

		// 显示失败的文件
		if (this.stats.failedFiles.length > 0) {
			console.log(chalk.red('\n❌ 失败的文件:'))
			this.stats.failedFiles.forEach(({ file, error }) => {
				console.log(chalk.red(`  ${file}: ${error}`))
			})
		}
	}

	getFailedFiles() {
		return this.stats.failedFiles
	}

	categorizeError (errorMessage) {
		if (errorMessage.includes('eventsApi') || errorMessage.includes('gogocode-plugin-vue')) {
			return 'gogocode-events-api'
		} else if (errorMessage.includes('gogocode')) {
			return 'gogocode-general'
		} else if (errorMessage.includes('SyntaxError')) {
			return 'syntax-error'
		} else if (errorMessage.includes('Cannot read properties')) {
			return 'property-access-error'
		} else {
			return 'unknown'
		}
	}

	/**
	 * 生成详细的错误报告
	 */
	generateErrorReport() {
		const errorStats = this.getTransformErrorStats()
		const failedFiles = this.getFailedFiles()

		const report = {
			summary: {
				total: this.stats.total,
				success: this.stats.success,
				skipped: this.stats.skipped,
				failed: this.stats.failed,
				successRate: ((this.stats.success / this.stats.total) * 100).toFixed(1)
			},
			transformErrors: errorStats,
			criticalFailures: failedFiles,
			recommendations: this.generateRecommendations(errorStats, failedFiles)
		}

		return report
	}

	/**
	 * 生成修复建议
	 */
	generateRecommendations(errorStats, failedFiles) {
		const recommendations = []

		// 基于错误类型的建议
		if (errorStats.byType['vue-transform'] > 0) {
			recommendations.push({
				type: 'version-compatibility',
				priority: 'high',
				message: '检查 gogocode-plugin-vue 版本兼容性',
				action: '升级或降级 gogocode-plugin-vue 到兼容版本'
			})
		}

		if (errorStats.byType['gogocode-ast'] > 0) {
			recommendations.push({
				type: 'syntax-issues',
				priority: 'medium',
				message: 'AST 解析错误，可能存在语法问题',
				action: '检查源代码语法，修复语法错误'
			})
		}

		if (failedFiles.length > this.stats.success) {
			recommendations.push({
				type: 'migration-strategy',
				priority: 'critical',
				message: '失败率过高，建议调整迁移策略',
				action: '考虑使用手动迁移或分步迁移'
			})
		}

		return recommendations
	}

	/**
	 * 保存错误报告到文件
	 */
	async saveErrorReport() {
		try {
			const report = this.generateErrorReport()
			const reportPath = path.join(this.projectPath, 'migration-error-report.json')

			await fs.writeJson(reportPath, report, { spaces: 2 })
			console.log(chalk.blue(`📋 错误报告已保存: file://${reportPath}`))

			return reportPath
		} catch (error) {
			console.warn(chalk.yellow(`⚠️  保存错误报告失败: ${error.message}`))
			return null
		}
	}

	/**
	 * 清理多余的 gogocodeTransfer.js 文件
	 */
	async cleanupGogocodeTransferFiles() {
		try {
			console.log(chalk.blue('🧹 清理多余的 gogocodeTransfer.js 文件...'))

			// 查找所有的 gogocodeTransfer.js 文件
			const gogocodeTransferFiles = glob.sync('**/utils/gogocodeTransfer.js', {
				cwd: this.inputPath,
				absolute: true,
				ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
			})

			// 保留主文件路径
			const mainGogocodeTransferFile = path.join(this.inputPath, 'src', 'utils', 'gogocodeTransfer.js')

			let cleanedCount = 0
			for (const filePath of gogocodeTransferFiles) {
				if (filePath !== mainGogocodeTransferFile) {
					try {
						await fs.remove(filePath)
						cleanedCount++

						const utilsDir = path.dirname(filePath)
						const utilsDirContents = await fs.readdir(utilsDir)
						if (utilsDirContents.length === 0) {
							await fs.remove(utilsDir)
						}
					} catch (error) {
						console.warn(chalk.yellow(`⚠️  删除文件失败: ${path.relative(this.inputPath, filePath)}`))
					}
				}
			}

			if (cleanedCount > 0) {
				console.log(chalk.green(`✅ 已清理 ${cleanedCount} 个多余的 gogocodeTransfer.js 文件`))
			} else {
				console.log(chalk.gray('📝 没有发现多余的 gogocodeTransfer.js 文件'))
			}

		} catch (error) {
			console.warn(chalk.yellow(`⚠️  清理 gogocodeTransfer.js 文件时出错: ${error.message}`))
		}
	}
}

module.exports = VueCodeTransformer
