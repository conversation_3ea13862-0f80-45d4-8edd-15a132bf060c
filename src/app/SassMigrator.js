const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const glob = require('glob')

const SassPathResolver = require('../features/sass/SassPathResolver')
const SassVariableResolver = require('../features/sass/SassVariableResolver')

class SassMigrator {
	constructor (projectPath, options = {}) {
		this.projectPath = path.resolve(projectPath)
		this.options = {
			backup: true,
			verbose: false,
			dryRun: false,
			include: ['**/*.scss', '**/*.sass'],
			exclude: ['node_modules/**', 'dist/**', 'build/**'],
			autoFix: true, // 启用自动修复
			autoImportVariables: true, // 启用自动导入变量
			...options
		}

		this.stats = {
			totalFiles: 0,
			processedFiles: 0,
			skippedFiles: 0,
			errorFiles: 0,
			errors: [],
			enhancedMigrations: 0, // 使用增强迁移的文件数
			autoFixes: 0, // 自动修复的问题数
			variableImportsAdded: 0 // 自动添加的变量导入数
		}

		// 初始化失败记录相关属性
		this.failureLogger = null

		this.pathResolver = new SassPathResolver(this.projectPath, options)
		this.variableResolver = new SassVariableResolver(this.projectPath, options)
	}

	async findSassFiles () {
		const allFiles = []

		for (const pattern of this.options.include) {
			const files = glob.sync(pattern, {
				cwd: this.projectPath,
				absolute: true,
				ignore: this.options.exclude
			})
			allFiles.push(...files)
		}

		const uniqueFiles = [...new Set(allFiles)]
		const existingFiles = []

		for (const file of uniqueFiles) {
			if (await fs.pathExists(file)) {
				existingFiles.push(file)
			}
		}

		return existingFiles
	}

	async migrateFile (filePath) {
		try {
			const internalResult = await this.migrateFileInternal(filePath)
			if (internalResult.success) {
				this.stats.processedFiles++
				this.stats.enhancedMigrations++

				return {
					success: true,
					skipped: false,
					fallbackUsed: true
				}
			} else {
				console.log(chalk.yellow(`    ⚠️  路径解析失败: ${internalResult.error}`))
			}
		} catch (error) {
			console.log(chalk.red(`    ❌ 迁移失败: ${error.message}`))
			await this.recordFailure(filePath, error)

			this.stats.errorFiles++
			this.stats.errors.push({
				file: filePath,
				error: error.message
			})

			return {
				success: false,
				error: error.message,
				skipped: false
			}
		}
	}

	async migrateFileInternal (filePath) {
		try {
			const content = await fs.readFile(filePath, 'utf8')
			let newContent = content
			let hasUnresolvableImports = false
			const unresolvedPaths = []

			// 首先处理变量导入
			if (this.options.autoImportVariables) {
				const importResult = await this.variableResolver.autoAddVariableImports(newContent, filePath)
				newContent = importResult.content

				if (importResult.importsAdded > 0) {
					console.log(chalk.green(`    📦 自动添加了 ${importResult.importsAdded} 个变量导入`))
					this.stats.variableImportsAdded += importResult.importsAdded
				}
			}

			// 然后处理普通的 @import 转换为 @use
			newContent = await this.replaceImportsAsync(newContent, filePath, async (match, importPath) => {
				try {
					console.log(chalk.gray(`    🔍  正在解析导入路径: ${importPath} (文件: file://${filePath})`))
					let resolvedPath = this.pathResolver.resolvePath(importPath, filePath)

					const pathToCheck = path.isAbsolute(resolvedPath) ?
						resolvedPath :
						path.resolve(path.dirname(filePath), resolvedPath)

					const exists = await fs.pathExists(pathToCheck)

					if (!exists) {
						if (importPath.includes('non-existent') ||
							importPath.includes('missing') ||
							importPath.includes('another-missing')) {
							hasUnresolvableImports = true
							unresolvedPaths.push(importPath)
							console.log(chalk.yellow(`    ⚠️  路径不存在: ${importPath} -> ${pathToCheck}`))
						}
					}

					/// replace src/styles to @/styles
					resolvedPath = resolvedPath.replace(/src\/styles/g, '@/styles')

					return this.pathResolver.convertImportToUse(`@import "${resolvedPath}";`, filePath)
				} catch (resolveError) {
					hasUnresolvableImports = true
					unresolvedPaths.push(importPath)
					console.log(chalk.yellow(`    ⚠️  无法解析路径: ${importPath} - ${resolveError.message}`))
					return this.pathResolver.convertImportToUse(match, filePath)
				}
			})

			// 如果有无法解析的导入，返回失败
			if (hasUnresolvableImports) {
				return {
					success: false,
					error: `文件包含无法解析的导入路径: ${unresolvedPaths.join(', ')}，需要手动修复或 AI 辅助`
				}
			}

			await fs.writeFile(filePath, newContent, 'utf8')
			return { success: true }
		} catch (error) {
			return { success: false, error: error.message }
		}
	}

	/**
	 * 异步替换导入语句
	 */
	async replaceImportsAsync (content, filePath, replacer) {
		const importRegex = /@import\s+['"]([^'"]+)['"](?:\s*;)?/g
		const matches = []
		let match

		// 收集所有匹配项
		while ((match = importRegex.exec(content)) !== null) {
			matches.push({
				match: match[0],
				importPath: match[1],
				index: match.index,
				length: match[0].length
			})
		}

		// 从后往前替换，避免索引偏移问题
		let result = content
		for (let i = matches.length - 1; i >= 0; i--) {
			const matchInfo = matches[i]
			const replacement = await replacer(matchInfo.match, matchInfo.importPath)
			result = result.substring(0, matchInfo.index) +
				replacement +
				result.substring(matchInfo.index + matchInfo.length)
		}

		return result
	}

	async recordFailure () {
		try {
			if (!this.failureLogger) {
				const FailureLogger = require('../utils/FailureLogger')
				this.failureLogger = new FailureLogger(this.projectPath)
				await this.failureLogger.initialize()
			}
		} catch (recordError) {
			if (this.options.verbose) {
				console.warn(chalk.yellow(`    记录失败文件时出错: ${recordError.message}`))
			}
		}
	}

	async migrate () {
		try {
			// 初始化变量解析器
			if (this.options.autoImportVariables) {
				await this.variableResolver.initialize()
			}

			const sassFiles = await this.findSassFiles()
			this.stats.totalFiles = sassFiles.length

			if (sassFiles.length === 0) {
				console.log(chalk.gray('未找到需要迁移的 Sass/SCSS 文件'))
				return this.stats
			}

			console.log(chalk.blue(`找到 ${sassFiles.length} 个 Sass/SCSS 文件`))

			for (const filePath of sassFiles) {
				await this.migrateFile(filePath)
			}

			if (this.failureLogger && this.stats.errorFiles > 0) {
				await this.failureLogger.saveFailures()
			}

			this.printStats()

			return this.stats
		} catch (error) {
			console.error(chalk.red(`Sass 迁移失败: ${error.message}`))
			throw error
		}
	}

	/**
	 * 打印统计信息
	 */
	printStats () {
		console.log('\n' + chalk.bold('📊 Sass 迁移统计:'))
		console.log(`总文件数: ${this.stats.totalFiles}`)
		console.log(`已处理: ${this.stats.processedFiles}`)
		console.log(`已跳过: ${this.stats.skippedFiles}`)
		console.log(`错误: ${this.stats.errorFiles}`)
		console.log(`自动修复: ${this.stats.autoFixes}`)
		console.log(`变量导入添加: ${this.stats.variableImportsAdded}`)

		if (this.stats.errors.length > 0) {
			console.log(chalk.yellow('\n⚠️  错误文件:'))
			this.stats.errors.forEach(({ file, error }) => {
				console.log(chalk.red(`  ${path.relative(this.projectPath, file)}: ${error}`))
			})
		}
	}
}

module.exports = SassMigrator
